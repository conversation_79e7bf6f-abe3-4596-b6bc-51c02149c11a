#!/usr/bin/env python3
"""
Advanced Reaction System Examples
Professional examples demonstrating all new features:
- Advanced Product Prediction
- Multiple Pathway Explorer
- Side Reaction Predictor
- Real Transition State Search
- Integrated Comprehensive Analysis
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def example_1_esterification_analysis():
    """Example 1: Complete analysis of esterification reaction."""
    print("\n" + "="*70)
    print("EXAMPLE 1: COMPREHENSIVE ESTERIFICATION ANALYSIS")
    print("="*70)
    print("Reaction: Ethanol + Acetic Acid → Ethyl Acetate + Water")
    
    from advanced_reaction_system import AdvancedReactionSystem
    
    # Initialize the system
    system = AdvancedReactionSystem(xc='b3lyp', basis='6-31g*')
    
    # Define reactants
    reactants = ['CCO', 'CC(=O)O']  # Ethanol + Acetic acid
    target_products = ['CC(=O)OCC']  # Ethyl acetate
    
    # Define reaction conditions
    conditions = {
        'temperature': 'moderate',
        'catalyst': 'acid',
        'solvent': 'organic'
    }
    
    # Perform comprehensive analysis
    analysis = system.analyze_reaction_comprehensive(
        reactant_smiles=reactants,
        target_products=target_products,
        reaction_conditions=conditions,
        include_ts_search=False  # Skip for example speed
    )
    
    # Export results
    system.export_comprehensive_analysis(analysis, 'esterification_analysis.json')
    
    print("\n✅ Example 1 completed successfully!")
    return analysis

def example_2_pathway_comparison():
    """Example 2: Compare multiple reaction pathways."""
    print("\n" + "="*70)
    print("EXAMPLE 2: MULTIPLE PATHWAY COMPARISON")
    print("="*70)
    print("Exploring different routes to amide formation")
    
    from multiple_pathway_explorer import MultiplePathwayExplorer
    
    explorer = MultiplePathwayExplorer()
    
    # Different starting materials for amide formation
    starting_materials_1 = ['CC(=O)O', 'CCN']  # Acid + Amine
    starting_materials_2 = ['CC(=O)Cl', 'CCN']  # Acid chloride + Amine
    
    target_products = ['CC(=O)NCC']  # N-ethylacetamide
    
    print("\nPathway Set 1: Carboxylic acid route")
    pathways_1 = explorer.explore_all_pathways(
        starting_materials=starting_materials_1,
        target_products=target_products,
        max_steps=2,
        max_pathways=3
    )
    
    print("\nPathway Set 2: Acid chloride route")
    pathways_2 = explorer.explore_all_pathways(
        starting_materials=starting_materials_2,
        target_products=target_products,
        max_steps=2,
        max_pathways=3
    )
    
    # Compare pathways
    print("\n📊 PATHWAY COMPARISON")
    print("-" * 40)
    
    if pathways_1:
        best_1 = pathways_1[0]
        print(f"Acid route - Yield: {best_1.overall_yield_estimate:.1%}, "
              f"Time: {best_1.total_time_estimate:.1f}h")
    
    if pathways_2:
        best_2 = pathways_2[0]
        print(f"Acid chloride route - Yield: {best_2.overall_yield_estimate:.1%}, "
              f"Time: {best_2.total_time_estimate:.1f}h")
    
    print("\n✅ Example 2 completed successfully!")
    return pathways_1, pathways_2

def example_3_side_reaction_optimization():
    """Example 3: Side reaction analysis and optimization."""
    print("\n" + "="*70)
    print("EXAMPLE 3: SIDE REACTION ANALYSIS & OPTIMIZATION")
    print("="*70)
    print("Optimizing selectivity for SN2 substitution")
    
    from side_reaction_predictor import SideReactionPredictor
    from real_product_predictor import ReactionPrediction
    
    predictor = SideReactionPredictor()
    
    # Define main reaction
    main_reaction = ReactionPrediction(
        reaction_type='sn2_substitution',
        description='Primary alkyl halide + nucleophile → substituted product',
        reactants=['CCCl', 'O'],  # Propyl chloride + hydroxide
        products=['CCO'],  # Propanol
        conditions={'temperature': 'moderate', 'solvent': 'polar_aprotic'},
        confidence=0.8,
        method='template_based'
    )
    
    print("\nAnalyzing side reactions under different conditions...")
    
    # Test different conditions
    condition_sets = [
        {'temperature': 'high', 'solvent': 'polar_protic'},
        {'temperature': 'moderate', 'solvent': 'polar_aprotic'},
        {'temperature': 'low', 'solvent': 'polar_aprotic'}
    ]
    
    best_selectivity = 0.0
    best_conditions = None
    
    for i, conditions in enumerate(condition_sets, 1):
        print(f"\nCondition Set {i}: {conditions}")
        
        analysis = predictor.predict_side_reactions(main_reaction, conditions)
        
        if analysis:
            selectivity = analysis.overall_selectivity_estimate
            print(f"   Selectivity: {selectivity:.1%}")
            print(f"   Side reactions: {len(analysis.side_reactions)}")
            
            if selectivity > best_selectivity:
                best_selectivity = selectivity
                best_conditions = conditions
    
    print(f"\n🏆 OPTIMAL CONDITIONS")
    print(f"Best selectivity: {best_selectivity:.1%}")
    print(f"Conditions: {best_conditions}")
    
    # Optimization suggestions
    optimization = predictor.optimize_selectivity(main_reaction, target_selectivity=0.9)
    print(f"\nOptimization result: {optimization}")
    
    print("\n✅ Example 3 completed successfully!")
    return best_conditions, optimization

def example_4_transition_state_comparison():
    """Example 4: Compare transition state search methods."""
    print("\n" + "="*70)
    print("EXAMPLE 4: TRANSITION STATE METHOD COMPARISON")
    print("="*70)
    print("Comparing TS search methods for a simple reaction")
    
    from transition_state_search import RealTransitionStateSearchEngine
    from input_handler import smiles_to_ase
    
    ts_engine = RealTransitionStateSearchEngine(xc='b3lyp', basis='sto-3g')
    
    # Simple test reaction
    reactant_smiles = 'CCO'  # Ethanol
    product_smiles = 'CC=O'  # Acetaldehyde (simplified)
    
    print(f"Reaction: {reactant_smiles} → {product_smiles}")
    
    try:
        reactant_atoms = smiles_to_ase(reactant_smiles)
        product_atoms = smiles_to_ase(product_smiles)
        
        if reactant_atoms and product_atoms:
            # Compare methods
            comparison = ts_engine.compare_ts_methods(reactant_atoms, product_atoms)
            
            print(f"\n📊 METHOD COMPARISON RESULTS")
            print(f"Successful methods: {comparison['successful_methods']}")
            print(f"Best method: {comparison['best_method']}")
            print(f"Recommendation: {comparison['recommendation']}")
            
            print("\n✅ Example 4 completed successfully!")
            return comparison
        else:
            print("❌ Could not create molecular structures")
            return None
            
    except Exception as e:
        print(f"❌ Example 4 failed: {e}")
        return None

def example_5_comprehensive_workflow():
    """Example 5: Complete professional workflow."""
    print("\n" + "="*70)
    print("EXAMPLE 5: COMPLETE PROFESSIONAL WORKFLOW")
    print("="*70)
    print("Full analysis workflow for pharmaceutical intermediate synthesis")
    
    from advanced_reaction_system import AdvancedReactionSystem
    
    # Initialize system
    system = AdvancedReactionSystem(xc='b3lyp', basis='6-31g*')
    
    # Define complex reaction system
    reactants = ['CC(=O)O', 'c1ccc(N)cc1']  # Acetic acid + Aniline
    target_products = ['CC(=O)Nc1ccccc1']  # Acetanilide
    
    conditions = {
        'temperature': 'moderate',
        'catalyst': 'coupling_agent',
        'solvent': 'aprotic',
        'atmosphere': 'inert'
    }
    
    print("Step 1: Quick feasibility check...")
    quick_check = system.quick_reaction_check(reactants)
    
    if not quick_check['feasible']:
        print(f"❌ Reaction not feasible: {quick_check['reason']}")
        return None
    
    print(f"✅ Quick check passed (confidence: {quick_check['confidence']:.2f})")
    
    print("\nStep 2: Comprehensive analysis...")
    comprehensive_analysis = system.analyze_reaction_comprehensive(
        reactant_smiles=reactants,
        target_products=target_products,
        reaction_conditions=conditions,
        include_ts_search=False  # Skip for example
    )
    
    print("\nStep 3: Export results...")
    system.export_comprehensive_analysis(
        comprehensive_analysis, 
        'pharmaceutical_synthesis_analysis.json'
    )
    
    print("\nStep 4: Generate report...")
    _generate_professional_report(comprehensive_analysis)
    
    print("\n✅ Example 5 completed successfully!")
    return comprehensive_analysis

def _generate_professional_report(analysis):
    """Generate a professional analysis report."""
    print("\n📋 PROFESSIONAL ANALYSIS REPORT")
    print("=" * 50)
    
    # Executive Summary
    print("EXECUTIVE SUMMARY")
    print("-" * 20)
    assessment = analysis.overall_assessment
    print(f"Reaction Feasibility: {assessment.get('feasibility', 'unknown').upper()}")
    print(f"Confidence Level: {assessment.get('confidence_level', 'unknown').upper()}")
    print(f"Selectivity: {assessment.get('selectivity', 'unknown').upper()}")
    print(f"Complexity: {assessment.get('complexity', 'unknown').upper()}")
    
    # Key Findings
    print(f"\nKEY FINDINGS")
    print("-" * 20)
    print(f"• {len(analysis.product_predictions)} viable product pathways identified")
    print(f"• {len(analysis.pathways)} reaction routes analyzed")
    
    if analysis.side_reaction_analysis:
        print(f"• {len(analysis.side_reaction_analysis.side_reactions)} potential side reactions")
        print(f"• Overall selectivity: {analysis.side_reaction_analysis.overall_selectivity_estimate:.1%}")
    
    # Recommendations
    print(f"\nRECOMMENDATIONS")
    print("-" * 20)
    for i, rec in enumerate(analysis.recommendations, 1):
        print(f"{i}. {rec}")

def run_all_examples():
    """Run all examples in sequence."""
    print("🚀 ADVANCED DFT REACTION SYSTEM - PROFESSIONAL EXAMPLES")
    print("=" * 70)
    
    examples = [
        ("Comprehensive Esterification Analysis", example_1_esterification_analysis),
        ("Multiple Pathway Comparison", example_2_pathway_comparison),
        ("Side Reaction Optimization", example_3_side_reaction_optimization),
        ("Transition State Method Comparison", example_4_transition_state_comparison),
        ("Complete Professional Workflow", example_5_comprehensive_workflow),
    ]
    
    results = {}
    
    for example_name, example_func in examples:
        print(f"\n{'='*10} {example_name} {'='*10}")
        try:
            result = example_func()
            results[example_name] = result is not None
        except Exception as e:
            print(f"❌ {example_name} failed: {e}")
            results[example_name] = False
    
    # Summary
    print(f"\n{'='*70}")
    print("🏁 EXAMPLES SUMMARY")
    print("=" * 70)
    
    for example_name, success in results.items():
        status = "✅ SUCCESS" if success else "❌ FAILED"
        print(f"{status} {example_name}")
    
    successful = sum(results.values())
    total = len(results)
    print(f"\nOverall: {successful}/{total} examples completed successfully")
    
    if successful == total:
        print("\n🎉 ALL EXAMPLES COMPLETED! The system is ready for professional use.")
    else:
        print("\n⚠️  Some examples had issues. Please review the output above.")

if __name__ == "__main__":
    run_all_examples()
