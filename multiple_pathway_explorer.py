"""
Multiple Pathway Explorer
Professional system for exploring and analyzing multiple reaction pathways simultaneously.
Handles competing mechanisms, alternative routes, and pathway optimization.
"""

import numpy as np
from rdkit import Chem
from rdkit.Chem import AllChem
from typing import List, Dict, Tuple, Optional, Set
from dataclasses import dataclass, asdict
import itertools
from collections import defaultdict, deque
import networkx as nx
import json
from real_product_predictor import AdvancedProductPredictor, ReactionPrediction

@dataclass
class ReactionPathway:
    """Data class for storing complete reaction pathways."""
    pathway_id: str
    steps: List[ReactionPrediction]
    overall_yield_estimate: float
    total_time_estimate: float  # hours
    complexity_score: float
    selectivity_issues: List[str]
    competing_pathways: List[str]
    optimization_suggestions: List[str]

@dataclass
class PathwayNode:
    """Node in the reaction network representing a chemical species."""
    smiles: str
    name: str
    is_starting_material: bool = False
    is_target: bool = False
    is_intermediate: bool = False
    stability_score: float = 0.5
    accessibility_score: float = 0.5

@dataclass
class PathwayEdge:
    """Edge in the reaction network representing a reaction step."""
    reactants: List[str]
    products: List[str]
    reaction_prediction: ReactionPrediction
    selectivity: float = 1.0
    yield_estimate: float = 0.8

class MultiplePathwayExplorer:
    """
    Advanced system for exploring multiple reaction pathways.
    
    Features:
    - Pathway enumeration and ranking
    - Competing mechanism analysis
    - Route optimization
    - Selectivity prediction
    - Network visualization
    """
    
    def __init__(self):
        """Initialize the pathway explorer."""
        self.product_predictor = AdvancedProductPredictor()
        self.reaction_network = nx.DiGraph()
        self.explored_molecules = set()
        self.pathway_cache = {}
        
    def explore_all_pathways(self, starting_materials: List[str], 
                           target_products: List[str] = None,
                           max_steps: int = 3,
                           max_pathways: int = 10) -> List[ReactionPathway]:
        """
        Explore all possible reaction pathways from starting materials.
        
        Args:
            starting_materials: List of starting material SMILES
            target_products: Optional target products to find routes to
            max_steps: Maximum number of reaction steps to explore
            max_pathways: Maximum number of pathways to return
            
        Returns:
            List of ReactionPathway objects ranked by feasibility
        """
        print(f"\n🗺️  MULTIPLE PATHWAY EXPLORATION")
        print("=" * 60)
        print(f"Starting materials: {', '.join(starting_materials)}")
        if target_products:
            print(f"Target products: {', '.join(target_products)}")
        print(f"Max steps: {max_steps}, Max pathways: {max_pathways}")
        
        # Initialize the reaction network
        self._initialize_network(starting_materials, target_products)
        
        # Explore pathways using breadth-first search
        all_pathways = self._explore_pathways_bfs(
            starting_materials, target_products, max_steps
        )
        
        # Analyze competing pathways
        analyzed_pathways = self._analyze_competing_pathways(all_pathways)
        
        # Rank pathways by multiple criteria
        ranked_pathways = self._rank_pathways(analyzed_pathways)
        
        # Display results
        self._display_pathway_results(ranked_pathways[:max_pathways])
        
        return ranked_pathways[:max_pathways]
    
    def _initialize_network(self, starting_materials: List[str], 
                          target_products: List[str] = None):
        """Initialize the reaction network with starting materials."""
        self.reaction_network.clear()
        self.explored_molecules.clear()
        
        # Add starting material nodes
        for smiles in starting_materials:
            node = PathwayNode(
                smiles=smiles,
                name=self._generate_molecule_name(smiles),
                is_starting_material=True,
                accessibility_score=1.0
            )
            self.reaction_network.add_node(smiles, **asdict(node))
            self.explored_molecules.add(smiles)
        
        # Add target product nodes if specified
        if target_products:
            for smiles in target_products:
                node = PathwayNode(
                    smiles=smiles,
                    name=self._generate_molecule_name(smiles),
                    is_target=True
                )
                self.reaction_network.add_node(smiles, **asdict(node))
    
    def _explore_pathways_bfs(self, starting_materials: List[str],
                            target_products: List[str] = None,
                            max_steps: int = 3) -> List[List[ReactionPrediction]]:
        """Explore pathways using breadth-first search."""
        pathways = []
        queue = deque()
        
        # Initialize queue with starting materials
        for start_combo in itertools.combinations(starting_materials, 2):
            queue.append((list(start_combo), []))
        
        step = 0
        while queue and step < max_steps:
            current_size = len(queue)
            
            for _ in range(current_size):
                current_reactants, pathway_so_far = queue.popleft()
                
                # Get predictions for current reactants
                predictions = self.product_predictor.predict_multiple_products(
                    current_reactants, max_products=5
                )
                
                for prediction in predictions:
                    if prediction.confidence < 0.3:  # Skip low-confidence predictions
                        continue
                    
                    new_pathway = pathway_so_far + [prediction]
                    
                    # Check if we've reached a target (if specified)
                    if target_products:
                        if any(prod in target_products for prod in prediction.products):
                            pathways.append(new_pathway)
                            continue
                    else:
                        # If no targets specified, collect all pathways
                        pathways.append(new_pathway)
                    
                    # Add products to network and continue exploration
                    for product in prediction.products:
                        if product not in self.explored_molecules:
                            self._add_molecule_to_network(product)
                            
                            # Continue exploration with this product
                            if len(new_pathway) < max_steps:
                                # Try reactions with other available molecules
                                for other_mol in list(self.explored_molecules):
                                    if other_mol != product:
                                        queue.append(([product, other_mol], new_pathway))
            
            step += 1
        
        return pathways
    
    def _add_molecule_to_network(self, smiles: str):
        """Add a new molecule to the reaction network."""
        if smiles in self.explored_molecules:
            return
        
        node = PathwayNode(
            smiles=smiles,
            name=self._generate_molecule_name(smiles),
            is_intermediate=True,
            stability_score=self._estimate_stability(smiles),
            accessibility_score=self._estimate_accessibility(smiles)
        )
        
        self.reaction_network.add_node(smiles, **asdict(node))
        self.explored_molecules.add(smiles)
    
    def _generate_molecule_name(self, smiles: str) -> str:
        """Generate a readable name for a molecule."""
        try:
            mol = Chem.MolFromSmiles(smiles)
            if mol:
                # Simple naming based on molecular formula
                formula = Chem.rdMolDescriptors.CalcMolFormula(mol)
                return f"Compound_{formula}"
            else:
                return f"Unknown_{hash(smiles) % 1000}"
        except:
            return f"Molecule_{hash(smiles) % 1000}"
    
    def _estimate_stability(self, smiles: str) -> float:
        """Estimate molecular stability (simplified)."""
        try:
            mol = Chem.MolFromSmiles(smiles)
            if not mol:
                return 0.1
            
            # Simple heuristics for stability
            num_rings = mol.GetRingInfo().NumRings()
            num_aromatic = sum(1 for atom in mol.GetAtoms() if atom.GetIsAromatic())
            
            stability = 0.5
            stability += min(num_rings * 0.1, 0.3)  # Rings add stability
            stability += min(num_aromatic * 0.05, 0.2)  # Aromaticity adds stability
            
            return min(stability, 1.0)
        except:
            return 0.3
    
    def _estimate_accessibility(self, smiles: str) -> float:
        """Estimate synthetic accessibility (simplified)."""
        try:
            mol = Chem.MolFromSmiles(smiles)
            if not mol:
                return 0.1
            
            # Simple accessibility based on complexity
            num_atoms = mol.GetNumAtoms()
            num_bonds = mol.GetNumBonds()
            
            # Simpler molecules are more accessible
            complexity = (num_atoms + num_bonds) / 20.0
            accessibility = max(0.1, 1.0 - complexity)
            
            return accessibility
        except:
            return 0.3

    def _analyze_competing_pathways(self, pathways: List[List[ReactionPrediction]]) -> List[ReactionPathway]:
        """Analyze competing pathways and identify selectivity issues."""
        analyzed_pathways = []

        for i, pathway_steps in enumerate(pathways):
            pathway_id = f"pathway_{i+1}"

            # Calculate overall metrics
            overall_yield = self._calculate_pathway_yield(pathway_steps)
            total_time = self._estimate_pathway_time(pathway_steps)
            complexity = self._calculate_complexity_score(pathway_steps)

            # Identify competing pathways
            competing = self._find_competing_pathways(pathway_steps, pathways)

            # Identify selectivity issues
            selectivity_issues = self._identify_selectivity_issues(pathway_steps)

            # Generate optimization suggestions
            optimizations = self._suggest_optimizations(pathway_steps)

            pathway = ReactionPathway(
                pathway_id=pathway_id,
                steps=pathway_steps,
                overall_yield_estimate=overall_yield,
                total_time_estimate=total_time,
                complexity_score=complexity,
                selectivity_issues=selectivity_issues,
                competing_pathways=competing,
                optimization_suggestions=optimizations
            )

            analyzed_pathways.append(pathway)

        return analyzed_pathways

    def _calculate_pathway_yield(self, steps: List[ReactionPrediction]) -> float:
        """Calculate overall yield for a pathway."""
        if not steps:
            return 0.0

        # Multiply individual step yields (assuming 80% yield per step as baseline)
        overall_yield = 1.0
        for step in steps:
            # Use confidence as a proxy for yield
            step_yield = 0.5 + (step.confidence * 0.4)  # 50-90% range
            overall_yield *= step_yield

        return overall_yield

    def _estimate_pathway_time(self, steps: List[ReactionPrediction]) -> float:
        """Estimate total time for pathway completion."""
        total_time = 0.0

        for step in steps:
            # Estimate time based on reaction type and conditions
            base_time = {
                'esterification': 4.0,
                'amidation': 6.0,
                'sn2_substitution': 2.0,
                'sn1_substitution': 1.0,
                'diels_alder': 8.0,
                'grignard_addition': 3.0,
                'aldol_condensation': 5.0
            }.get(step.reaction_type, 4.0)

            # Adjust based on conditions
            if 'high' in str(step.conditions.get('temperature', '')):
                base_time *= 0.7  # High temp = faster
            if 'catalyst' in step.conditions:
                base_time *= 0.8  # Catalyst = faster

            total_time += base_time

        return total_time

    def _calculate_complexity_score(self, steps: List[ReactionPrediction]) -> float:
        """Calculate complexity score for a pathway."""
        if not steps:
            return 0.0

        complexity = 0.0

        # Number of steps adds complexity
        complexity += len(steps) * 0.2

        # Difficult reaction types add complexity
        for step in steps:
            difficulty = {
                'esterification': 0.1,
                'amidation': 0.2,
                'sn2_substitution': 0.1,
                'sn1_substitution': 0.3,
                'diels_alder': 0.4,
                'grignard_addition': 0.3,
                'aldol_condensation': 0.3
            }.get(step.reaction_type, 0.2)

            complexity += difficulty

        return min(complexity, 1.0)

    def _find_competing_pathways(self, target_pathway: List[ReactionPrediction],
                               all_pathways: List[List[ReactionPrediction]]) -> List[str]:
        """Find pathways that compete with the target pathway."""
        competing = []

        target_reactants = set()
        for step in target_pathway:
            target_reactants.update(step.reactants)

        for i, other_pathway in enumerate(all_pathways):
            if other_pathway == target_pathway:
                continue

            other_reactants = set()
            for step in other_pathway:
                other_reactants.update(step.reactants)

            # Check for overlap in reactants
            if target_reactants & other_reactants:
                competing.append(f"pathway_{i+1}")

        return competing

    def _identify_selectivity_issues(self, steps: List[ReactionPrediction]) -> List[str]:
        """Identify potential selectivity issues in a pathway."""
        issues = []

        for i, step in enumerate(steps):
            # Check for side products
            if step.side_products:
                issues.append(f"Step {i+1}: Side products possible - {', '.join(step.side_products)}")

            # Check for regioselectivity issues
            if 'substitution' in step.reaction_type and len(step.reactants) > 1:
                issues.append(f"Step {i+1}: Potential regioselectivity issues")

            # Check for stereoselectivity issues
            if 'addition' in step.reaction_type:
                issues.append(f"Step {i+1}: Consider stereoselectivity")

        return issues

    def _suggest_optimizations(self, steps: List[ReactionPrediction]) -> List[str]:
        """Suggest optimizations for a pathway."""
        suggestions = []

        for i, step in enumerate(steps):
            # Temperature optimization
            if 'high' in str(step.conditions.get('temperature', '')):
                suggestions.append(f"Step {i+1}: Consider microwave heating for efficiency")

            # Catalyst suggestions
            if step.activation_energy_estimate and step.activation_energy_estimate > 20:
                suggestions.append(f"Step {i+1}: High barrier - consider catalyst screening")

            # Solvent optimization
            if 'solvent' in step.conditions:
                suggestions.append(f"Step {i+1}: Optimize solvent for better selectivity")

        return suggestions

    def _rank_pathways(self, pathways: List[ReactionPathway]) -> List[ReactionPathway]:
        """Rank pathways by multiple criteria."""
        def pathway_score(pathway):
            # Multi-criteria scoring
            yield_score = pathway.overall_yield_estimate
            time_score = max(0, 1.0 - pathway.total_time_estimate / 24.0)  # Prefer <24h
            complexity_score = max(0, 1.0 - pathway.complexity_score)
            selectivity_score = max(0, 1.0 - len(pathway.selectivity_issues) * 0.1)

            # Weighted average
            total_score = (
                yield_score * 0.4 +
                time_score * 0.2 +
                complexity_score * 0.2 +
                selectivity_score * 0.2
            )

            return total_score

        return sorted(pathways, key=pathway_score, reverse=True)

    def _display_pathway_results(self, pathways: List[ReactionPathway]):
        """Display pathway exploration results."""
        print(f"\n🎯 PATHWAY ANALYSIS RESULTS")
        print("-" * 60)

        for i, pathway in enumerate(pathways):
            print(f"\n{i+1}. {pathway.pathway_id.upper()}")
            print(f"   Steps: {len(pathway.steps)}")
            print(f"   Est. yield: {pathway.overall_yield_estimate:.1%}")
            print(f"   Est. time: {pathway.total_time_estimate:.1f} hours")
            print(f"   Complexity: {pathway.complexity_score:.2f}")

            if pathway.competing_pathways:
                print(f"   Competes with: {', '.join(pathway.competing_pathways)}")

            if pathway.selectivity_issues:
                print(f"   Selectivity issues: {len(pathway.selectivity_issues)}")

            if pathway.optimization_suggestions:
                print(f"   Optimization opportunities: {len(pathway.optimization_suggestions)}")

            # Show reaction sequence
            print("   Reaction sequence:")
            for j, step in enumerate(pathway.steps):
                reactants = " + ".join(step.reactants)
                products = " + ".join(step.products)
                print(f"     {j+1}. {reactants} → {products}")
                print(f"        ({step.reaction_type}, conf: {step.confidence:.2f})")

    def get_pathway_network(self) -> nx.DiGraph:
        """Get the complete reaction network for visualization."""
        return self.reaction_network

    def export_pathways_json(self, pathways: List[ReactionPathway], filename: str):
        """Export pathways to JSON file."""
        pathway_data = []
        for pathway in pathways:
            pathway_dict = asdict(pathway)
            # Convert ReactionPrediction objects to dicts
            pathway_dict['steps'] = [asdict(step) for step in pathway.steps]
            pathway_data.append(pathway_dict)

        with open(filename, 'w') as f:
            json.dump(pathway_data, f, indent=2)

        print(f"✅ Pathways exported to {filename}")

    def analyze_pathway_selectivity(self, pathway: ReactionPathway) -> Dict:
        """Detailed selectivity analysis for a specific pathway."""
        analysis = {
            'overall_selectivity_score': 1.0,
            'step_selectivities': [],
            'major_concerns': [],
            'recommendations': []
        }

        for i, step in enumerate(pathway.steps):
            step_analysis = {
                'step_number': i + 1,
                'reaction_type': step.reaction_type,
                'selectivity_score': 1.0,
                'issues': []
            }

            # Analyze different types of selectivity
            if 'substitution' in step.reaction_type:
                step_analysis['selectivity_score'] *= 0.8  # Substitution can have issues
                step_analysis['issues'].append('Regioselectivity concerns')

            if 'addition' in step.reaction_type:
                step_analysis['selectivity_score'] *= 0.9
                step_analysis['issues'].append('Stereoselectivity considerations')

            if step.side_products:
                step_analysis['selectivity_score'] *= 0.7
                step_analysis['issues'].append('Competing side reactions')

            analysis['step_selectivities'].append(step_analysis)
            analysis['overall_selectivity_score'] *= step_analysis['selectivity_score']

        # Generate recommendations
        if analysis['overall_selectivity_score'] < 0.7:
            analysis['recommendations'].append("Consider alternative reaction conditions")
            analysis['recommendations'].append("Screen for selective catalysts")

        return analysis
