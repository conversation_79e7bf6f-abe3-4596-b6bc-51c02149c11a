"""
Side Reaction Predictor
Professional system for predicting and analyzing competing side reactions.
Identifies potential unwanted pathways and suggests mitigation strategies.
"""

import numpy as np
from rdkit import Chem
from rdkit.Chem import AllChem, Descriptors, rdMolDescriptors
from typing import List, Dict, Tuple, Optional, Set
from dataclasses import dataclass, asdict
from collections import defaultdict
import itertools
from real_product_predictor import AdvancedProductPredictor, ReactionPrediction

@dataclass
class SideReaction:
    """Data class for storing side reaction information."""
    reaction_type: str
    description: str
    reactants: List[str]
    side_products: List[str]
    probability: float
    severity: str  # 'low', 'medium', 'high'
    mechanism: str
    conditions_favoring: Dict[str, str]
    mitigation_strategies: List[str]
    thermodynamic_favorability: float = 0.0
    kinetic_competitiveness: float = 0.0

@dataclass
class SideReactionAnalysis:
    """Complete analysis of side reactions for a main reaction."""
    main_reaction: ReactionPrediction
    side_reactions: List[SideReaction]
    overall_selectivity_estimate: float
    major_concerns: List[str]
    recommended_conditions: Dict[str, str]
    monitoring_suggestions: List[str]

class SideReactionPredictor:
    """
    Professional system for predicting competing side reactions.
    
    Features:
    - Comprehensive side reaction database
    - Mechanistic analysis of competing pathways
    - Selectivity prediction and optimization
    - Condition-dependent side reaction analysis
    - Mitigation strategy recommendations
    """
    
    def __init__(self):
        """Initialize the side reaction predictor."""
        self.product_predictor = AdvancedProductPredictor()
        self.side_reaction_database = self._load_side_reaction_database()
        self.selectivity_rules = self._load_selectivity_rules()
        self.condition_effects = self._load_condition_effects()
    
    def _load_side_reaction_database(self) -> Dict[str, List[Dict]]:
        """Load comprehensive database of known side reactions."""
        return {
            'esterification': [
                {
                    'type': 'dehydration',
                    'description': 'Alcohol dehydration to alkene',
                    'conditions': {'temperature': 'high', 'acid': 'strong'},
                    'probability': 0.3,
                    'severity': 'medium',
                    'mechanism': 'E1/E2 elimination',
                    'mitigation': ['lower_temperature', 'weaker_acid', 'shorter_time']
                },
                {
                    'type': 'ether_formation',
                    'description': 'Alcohol condensation to ether',
                    'conditions': {'temperature': 'high', 'acid': 'present'},
                    'probability': 0.2,
                    'severity': 'low',
                    'mechanism': 'SN2 displacement',
                    'mitigation': ['lower_temperature', 'excess_carboxylic_acid']
                },
                {
                    'type': 'transesterification',
                    'description': 'Exchange of ester groups',
                    'conditions': {'ester_present': True, 'catalyst': 'present'},
                    'probability': 0.15,
                    'severity': 'low',
                    'mechanism': 'nucleophilic_acyl_substitution',
                    'mitigation': ['remove_other_esters', 'control_stoichiometry']
                }
            ],
            'amidation': [
                {
                    'type': 'salt_formation',
                    'description': 'Acid-base salt formation without condensation',
                    'conditions': {'temperature': 'low', 'no_coupling_agent': True},
                    'probability': 0.8,
                    'severity': 'high',
                    'mechanism': 'acid_base_reaction',
                    'mitigation': ['use_coupling_agent', 'activate_carboxylic_acid']
                },
                {
                    'type': 'anhydride_formation',
                    'description': 'Carboxylic acid dimerization',
                    'conditions': {'temperature': 'high', 'dehydrating_agent': True},
                    'probability': 0.25,
                    'severity': 'medium',
                    'mechanism': 'condensation',
                    'mitigation': ['control_temperature', 'add_amine_early']
                }
            ],
            'sn2_substitution': [
                {
                    'type': 'elimination',
                    'description': 'E2 elimination to alkene',
                    'conditions': {'base_strength': 'strong', 'temperature': 'high'},
                    'probability': 0.4,
                    'severity': 'high',
                    'mechanism': 'E2_elimination',
                    'mitigation': ['weaker_base', 'lower_temperature', 'polar_aprotic_solvent']
                },
                {
                    'type': 'rearrangement',
                    'description': 'Carbocation rearrangement',
                    'conditions': {'substrate': 'secondary', 'solvent': 'protic'},
                    'probability': 0.2,
                    'severity': 'medium',
                    'mechanism': 'SN1_pathway',
                    'mitigation': ['polar_aprotic_solvent', 'primary_substrate']
                }
            ],
            'diels_alder': [
                {
                    'type': 'ene_reaction',
                    'description': 'Alternative pericyclic pathway',
                    'conditions': {'temperature': 'very_high', 'dienophile': 'electron_poor'},
                    'probability': 0.15,
                    'severity': 'low',
                    'mechanism': 'pericyclic',
                    'mitigation': ['moderate_temperature', 'electron_rich_dienophile']
                },
                {
                    'type': 'polymerization',
                    'description': 'Multiple cycloadditions',
                    'conditions': {'concentration': 'high', 'time': 'long'},
                    'probability': 0.1,
                    'severity': 'medium',
                    'mechanism': 'sequential_cycloaddition',
                    'mitigation': ['dilute_conditions', 'shorter_time', 'stoichiometric_control']
                }
            ],
            'grignard_addition': [
                {
                    'type': 'reduction',
                    'description': 'Hydride delivery instead of alkyl',
                    'conditions': {'grignard': 'secondary', 'carbonyl': 'hindered'},
                    'probability': 0.3,
                    'severity': 'high',
                    'mechanism': 'beta_hydride_elimination',
                    'mitigation': ['primary_grignard', 'unhindered_carbonyl', 'low_temperature']
                },
                {
                    'type': 'enolate_formation',
                    'description': 'Deprotonation alpha to carbonyl',
                    'conditions': {'carbonyl': 'ketone', 'alpha_protons': True},
                    'probability': 0.2,
                    'severity': 'medium',
                    'mechanism': 'deprotonation',
                    'mitigation': ['protect_alpha_position', 'aldehyde_substrate']
                }
            ],
            'aldol_condensation': [
                {
                    'type': 'self_condensation',
                    'description': 'Homo-aldol reaction',
                    'conditions': {'single_carbonyl': True, 'base': 'strong'},
                    'probability': 0.6,
                    'severity': 'high',
                    'mechanism': 'enolate_formation_addition',
                    'mitigation': ['mixed_aldol_conditions', 'kinetic_control', 'LDA']
                },
                {
                    'type': 'polymerization',
                    'description': 'Multiple aldol reactions',
                    'conditions': {'time': 'long', 'temperature': 'high'},
                    'probability': 0.3,
                    'severity': 'medium',
                    'mechanism': 'sequential_aldol',
                    'mitigation': ['kinetic_quench', 'lower_temperature', 'dilute_conditions']
                }
            ]
        }
    
    def _load_selectivity_rules(self) -> Dict[str, Dict]:
        """Load rules for predicting selectivity based on conditions."""
        return {
            'temperature_effects': {
                'low': {'kinetic_control': True, 'selectivity': 'high'},
                'moderate': {'mixed_control': True, 'selectivity': 'medium'},
                'high': {'thermodynamic_control': True, 'selectivity': 'low'}
            },
            'solvent_effects': {
                'polar_protic': {'sn1_favored': True, 'elimination_increased': True},
                'polar_aprotic': {'sn2_favored': True, 'elimination_decreased': True},
                'nonpolar': {'radical_reactions': True, 'pericyclic_favored': True}
            },
            'concentration_effects': {
                'high': {'bimolecular_favored': True, 'side_reactions_increased': True},
                'low': {'unimolecular_favored': True, 'selectivity_improved': True}
            }
        }
    
    def _load_condition_effects(self) -> Dict[str, Dict]:
        """Load how different conditions affect side reaction probabilities."""
        return {
            'temperature': {
                'increase': ['elimination', 'rearrangement', 'decomposition'],
                'decrease': ['slow_reactions', 'incomplete_conversion']
            },
            'concentration': {
                'increase': ['polymerization', 'bimolecular_side_reactions'],
                'decrease': ['slow_reactions', 'incomplete_conversion']
            },
            'catalyst_loading': {
                'increase': ['over_reaction', 'side_catalysis'],
                'decrease': ['slow_reactions', 'incomplete_conversion']
            }
        }
    
    def predict_side_reactions(self, main_reaction: ReactionPrediction,
                             reaction_conditions: Dict[str, str] = None) -> SideReactionAnalysis:
        """
        Predict all possible side reactions for a main reaction.
        
        Args:
            main_reaction: The main reaction prediction
            reaction_conditions: Specific reaction conditions
            
        Returns:
            Complete side reaction analysis
        """
        print(f"\n⚠️  SIDE REACTION ANALYSIS")
        print("=" * 50)
        print(f"Main reaction: {main_reaction.reaction_type}")
        print(f"Reactants: {' + '.join(main_reaction.reactants)}")
        print(f"Products: {' + '.join(main_reaction.products)}")
        
        if reaction_conditions:
            print(f"Conditions: {reaction_conditions}")
        
        # Get known side reactions for this reaction type
        known_side_reactions = self._get_known_side_reactions(
            main_reaction, reaction_conditions
        )
        
        # Predict additional side reactions based on functional groups
        predicted_side_reactions = self._predict_functional_group_side_reactions(
            main_reaction, reaction_conditions
        )
        
        # Combine and analyze all side reactions
        all_side_reactions = known_side_reactions + predicted_side_reactions
        
        # Calculate overall selectivity
        overall_selectivity = self._calculate_overall_selectivity(
            main_reaction, all_side_reactions
        )
        
        # Identify major concerns
        major_concerns = self._identify_major_concerns(all_side_reactions)
        
        # Recommend optimal conditions
        recommended_conditions = self._recommend_conditions(
            main_reaction, all_side_reactions
        )
        
        # Suggest monitoring strategies
        monitoring_suggestions = self._suggest_monitoring(all_side_reactions)
        
        analysis = SideReactionAnalysis(
            main_reaction=main_reaction,
            side_reactions=all_side_reactions,
            overall_selectivity_estimate=overall_selectivity,
            major_concerns=major_concerns,
            recommended_conditions=recommended_conditions,
            monitoring_suggestions=monitoring_suggestions
        )
        
        self._display_side_reaction_analysis(analysis)
        
        return analysis

    def _get_known_side_reactions(self, main_reaction: ReactionPrediction,
                                conditions: Dict[str, str] = None) -> List[SideReaction]:
        """Get known side reactions for the main reaction type."""
        side_reactions = []

        reaction_type = main_reaction.reaction_type
        if reaction_type not in self.side_reaction_database:
            return side_reactions

        for side_data in self.side_reaction_database[reaction_type]:
            # Check if conditions favor this side reaction
            probability = side_data['probability']

            if conditions:
                probability = self._adjust_probability_for_conditions(
                    probability, side_data['conditions'], conditions
                )

            # Create side products based on reactants
            side_products = self._generate_side_products(
                main_reaction.reactants, side_data['type'], side_data['mechanism']
            )

            side_reaction = SideReaction(
                reaction_type=side_data['type'],
                description=side_data['description'],
                reactants=main_reaction.reactants,
                side_products=side_products,
                probability=probability,
                severity=side_data['severity'],
                mechanism=side_data['mechanism'],
                conditions_favoring=side_data['conditions'],
                mitigation_strategies=side_data['mitigation']
            )

            side_reactions.append(side_reaction)

        return side_reactions

    def _predict_functional_group_side_reactions(self, main_reaction: ReactionPrediction,
                                               conditions: Dict[str, str] = None) -> List[SideReaction]:
        """Predict additional side reactions based on functional group analysis."""
        side_reactions = []

        # Analyze functional groups in reactants
        reactant_mols = [Chem.MolFromSmiles(s) for s in main_reaction.reactants
                        if Chem.MolFromSmiles(s)]

        for mol in reactant_mols:
            if not mol:
                continue

            # Check for reactive functional groups that could lead to side reactions
            fgs = self.product_predictor.identify_functional_groups(mol)

            for fg in fgs:
                potential_sides = self._get_functional_group_side_reactions(fg, conditions)
                side_reactions.extend(potential_sides)

        return side_reactions

    def _adjust_probability_for_conditions(self, base_probability: float,
                                         favoring_conditions: Dict[str, str],
                                         actual_conditions: Dict[str, str]) -> float:
        """Adjust side reaction probability based on actual conditions."""
        adjusted_prob = base_probability

        for condition, favoring_value in favoring_conditions.items():
            if condition in actual_conditions:
                actual_value = actual_conditions[condition]

                # Simple heuristic adjustments
                if condition == 'temperature':
                    if favoring_value == 'high' and actual_value == 'high':
                        adjusted_prob *= 1.5
                    elif favoring_value == 'high' and actual_value == 'low':
                        adjusted_prob *= 0.5
                elif condition == 'acid' and favoring_value == 'strong':
                    if 'strong' in actual_value:
                        adjusted_prob *= 1.3
                    elif 'weak' in actual_value:
                        adjusted_prob *= 0.7

        return min(adjusted_prob, 1.0)

    def _generate_side_products(self, reactants: List[str], side_type: str,
                              mechanism: str) -> List[str]:
        """Generate side products based on reaction type and mechanism."""
        side_products = []

        # Simplified side product generation
        if side_type == 'dehydration':
            side_products = ['O']  # Water
        elif side_type == 'elimination':
            side_products = ['alkene_product']
        elif side_type == 'ether_formation':
            side_products = ['ether_product', 'O']
        elif side_type == 'salt_formation':
            side_products = ['salt_product']
        elif side_type == 'polymerization':
            side_products = ['polymer_product']
        elif side_type == 'rearrangement':
            side_products = ['rearranged_product']
        elif side_type == 'reduction':
            side_products = ['reduced_product']
        else:
            side_products = ['unknown_side_product']

        return side_products

    def _get_functional_group_side_reactions(self, functional_group: str,
                                           conditions: Dict[str, str] = None) -> List[SideReaction]:
        """Get potential side reactions for specific functional groups."""
        side_reactions = []

        fg_side_reactions = {
            'alcohol': [
                {
                    'type': 'dehydration',
                    'description': 'Alcohol elimination to alkene',
                    'probability': 0.2,
                    'severity': 'medium',
                    'mechanism': 'E1/E2',
                    'conditions': {'temperature': 'high', 'acid': 'present'}
                }
            ],
            'aldehyde': [
                {
                    'type': 'aldol_self_condensation',
                    'description': 'Self-condensation of aldehyde',
                    'probability': 0.3,
                    'severity': 'medium',
                    'mechanism': 'enolate_chemistry',
                    'conditions': {'base': 'present'}
                }
            ],
            'amine': [
                {
                    'type': 'oxidation',
                    'description': 'Amine oxidation to imine/nitrile',
                    'probability': 0.15,
                    'severity': 'low',
                    'mechanism': 'oxidation',
                    'conditions': {'oxidizing_agent': 'present'}
                }
            ]
        }

        if functional_group in fg_side_reactions:
            for side_data in fg_side_reactions[functional_group]:
                side_reaction = SideReaction(
                    reaction_type=side_data['type'],
                    description=side_data['description'],
                    reactants=[],  # Will be filled based on context
                    side_products=self._generate_side_products([], side_data['type'], side_data['mechanism']),
                    probability=side_data['probability'],
                    severity=side_data['severity'],
                    mechanism=side_data['mechanism'],
                    conditions_favoring=side_data['conditions'],
                    mitigation_strategies=['optimize_conditions', 'use_protecting_groups']
                )
                side_reactions.append(side_reaction)

        return side_reactions

    def _calculate_overall_selectivity(self, main_reaction: ReactionPrediction,
                                     side_reactions: List[SideReaction]) -> float:
        """Calculate overall selectivity considering all side reactions."""
        if not side_reactions:
            return 0.95  # High selectivity if no side reactions

        # Start with main reaction probability (use confidence as proxy)
        main_prob = main_reaction.confidence

        # Calculate total side reaction probability
        total_side_prob = sum(sr.probability for sr in side_reactions)

        # Normalize probabilities
        total_prob = main_prob + total_side_prob
        if total_prob > 0:
            selectivity = main_prob / total_prob
        else:
            selectivity = 0.5

        return min(selectivity, 1.0)

    def _identify_major_concerns(self, side_reactions: List[SideReaction]) -> List[str]:
        """Identify the most concerning side reactions."""
        concerns = []

        for sr in side_reactions:
            if sr.probability > 0.3 and sr.severity in ['medium', 'high']:
                concerns.append(f"{sr.reaction_type}: {sr.description} (prob: {sr.probability:.2f})")

        # Sort by probability
        concerns.sort(key=lambda x: float(x.split('prob: ')[1].split(')')[0]), reverse=True)

        return concerns[:5]  # Top 5 concerns

    def _recommend_conditions(self, main_reaction: ReactionPrediction,
                            side_reactions: List[SideReaction]) -> Dict[str, str]:
        """Recommend optimal conditions to minimize side reactions."""
        recommendations = {}

        # Analyze what conditions minimize side reactions
        temp_preferences = []
        solvent_preferences = []
        catalyst_preferences = []

        for sr in side_reactions:
            if sr.probability > 0.2:  # Only consider significant side reactions
                for mitigation in sr.mitigation_strategies:
                    if 'temperature' in mitigation:
                        if 'lower' in mitigation:
                            temp_preferences.append('moderate')
                        elif 'higher' in mitigation:
                            temp_preferences.append('high')
                    elif 'solvent' in mitigation:
                        if 'aprotic' in mitigation:
                            solvent_preferences.append('polar_aprotic')
                        elif 'protic' in mitigation:
                            solvent_preferences.append('polar_protic')
                    elif 'catalyst' in mitigation:
                        catalyst_preferences.append('selective_catalyst')

        # Make recommendations based on most common preferences
        if temp_preferences:
            most_common_temp = max(set(temp_preferences), key=temp_preferences.count)
            recommendations['temperature'] = most_common_temp

        if solvent_preferences:
            most_common_solvent = max(set(solvent_preferences), key=solvent_preferences.count)
            recommendations['solvent'] = most_common_solvent

        if catalyst_preferences:
            recommendations['catalyst'] = 'screen_for_selectivity'

        # Add general recommendations
        recommendations['concentration'] = 'moderate'
        recommendations['time'] = 'monitor_closely'
        recommendations['atmosphere'] = 'inert_if_needed'

        return recommendations

    def _suggest_monitoring(self, side_reactions: List[SideReaction]) -> List[str]:
        """Suggest monitoring strategies for side reactions."""
        suggestions = []

        # General monitoring
        suggestions.append("Monitor reaction progress by TLC or HPLC")
        suggestions.append("Check for color changes indicating side reactions")

        # Specific monitoring based on side reaction types
        side_types = [sr.reaction_type for sr in side_reactions]

        if 'elimination' in side_types:
            suggestions.append("Monitor for alkene formation by NMR")

        if 'polymerization' in side_types:
            suggestions.append("Check for viscosity increase or precipitation")

        if 'oxidation' in side_types:
            suggestions.append("Monitor for color changes indicating oxidation")

        if 'rearrangement' in side_types:
            suggestions.append("Use NMR to detect structural rearrangements")

        return suggestions

    def _display_side_reaction_analysis(self, analysis: SideReactionAnalysis):
        """Display comprehensive side reaction analysis."""
        print(f"\n📊 SIDE REACTION ANALYSIS RESULTS")
        print("-" * 50)
        print(f"Overall selectivity estimate: {analysis.overall_selectivity_estimate:.1%}")
        print(f"Number of potential side reactions: {len(analysis.side_reactions)}")

        if analysis.major_concerns:
            print(f"\n⚠️  MAJOR CONCERNS:")
            for concern in analysis.major_concerns:
                print(f"   • {concern}")

        print(f"\n🔬 SIDE REACTIONS IDENTIFIED:")
        for i, sr in enumerate(analysis.side_reactions[:5]):  # Show top 5
            print(f"\n{i+1}. {sr.reaction_type.upper()}")
            print(f"   Description: {sr.description}")
            print(f"   Probability: {sr.probability:.2f}")
            print(f"   Severity: {sr.severity}")
            print(f"   Mechanism: {sr.mechanism}")

            if sr.mitigation_strategies:
                print(f"   Mitigation: {', '.join(sr.mitigation_strategies[:3])}")

        print(f"\n💡 RECOMMENDED CONDITIONS:")
        for condition, value in analysis.recommended_conditions.items():
            print(f"   {condition}: {value}")

        print(f"\n🔍 MONITORING SUGGESTIONS:")
        for suggestion in analysis.monitoring_suggestions[:3]:
            print(f"   • {suggestion}")

    def optimize_selectivity(self, main_reaction: ReactionPrediction,
                           target_selectivity: float = 0.9) -> Dict:
        """
        Optimize reaction conditions to achieve target selectivity.

        Args:
            main_reaction: The main reaction to optimize
            target_selectivity: Desired selectivity (0-1)

        Returns:
            Optimization recommendations
        """
        print(f"\n🎯 SELECTIVITY OPTIMIZATION")
        print(f"Target selectivity: {target_selectivity:.1%}")

        # Test different condition sets
        condition_sets = [
            {'temperature': 'low', 'solvent': 'polar_aprotic', 'concentration': 'moderate'},
            {'temperature': 'moderate', 'solvent': 'polar_protic', 'concentration': 'low'},
            {'temperature': 'high', 'solvent': 'nonpolar', 'concentration': 'high'},
        ]

        best_conditions = None
        best_selectivity = 0.0

        for conditions in condition_sets:
            analysis = self.predict_side_reactions(main_reaction, conditions)

            if analysis.overall_selectivity_estimate > best_selectivity:
                best_selectivity = analysis.overall_selectivity_estimate
                best_conditions = conditions

        optimization_result = {
            'best_conditions': best_conditions,
            'predicted_selectivity': best_selectivity,
            'target_achieved': best_selectivity >= target_selectivity,
            'improvement_needed': max(0, target_selectivity - best_selectivity)
        }

        if optimization_result['target_achieved']:
            print(f"✅ Target selectivity achievable: {best_selectivity:.1%}")
        else:
            print(f"⚠️  Target not achieved. Best: {best_selectivity:.1%}")
            print(f"   Additional optimization needed: {optimization_result['improvement_needed']:.1%}")

        return optimization_result

    def export_side_reaction_report(self, analysis: SideReactionAnalysis, filename: str):
        """Export detailed side reaction report."""
        report_data = {
            'main_reaction': asdict(analysis.main_reaction),
            'side_reactions': [asdict(sr) for sr in analysis.side_reactions],
            'overall_selectivity': analysis.overall_selectivity_estimate,
            'major_concerns': analysis.major_concerns,
            'recommended_conditions': analysis.recommended_conditions,
            'monitoring_suggestions': analysis.monitoring_suggestions
        }

        import json
        with open(filename, 'w') as f:
            json.dump(report_data, f, indent=2)

        print(f"✅ Side reaction report exported to {filename}")
