"""
Advanced Reaction System Integration
Professional integration of all advanced features:
- Advanced Product Prediction
- Multiple Pathway Explorer  
- Side Reaction Predictor
- Real Transition State Search
"""

import numpy as np
from typing import List, Dict, Tuple, Optional
from dataclasses import dataclass, asdict
import json
from ase import Atoms

# Import all advanced modules
from real_product_predictor import AdvancedProductPredictor, ReactionPrediction
from multiple_pathway_explorer import MultiplePathwayExplorer, ReactionPathway
from side_reaction_predictor import SideReactionPredictor, SideReactionAnalysis
from transition_state_search import RealTransitionStateSearchEngine, TransitionStateSearchResult
from input_handler import smiles_to_ase
from molecule_optimizer import optimize_geometry

@dataclass
class ComprehensiveReactionAnalysis:
    """Complete analysis of a reaction system."""
    reactants: List[str]
    product_predictions: List[ReactionPrediction]
    pathways: List[ReactionPathway]
    side_reaction_analysis: SideReactionAnalysis
    transition_states: Dict[str, TransitionStateSearchResult]
    overall_assessment: Dict[str, any]
    recommendations: List[str]

class AdvancedReactionSystem:
    """
    Professional reaction analysis system integrating all advanced features.
    
    This is the main interface for comprehensive reaction analysis that goes
    far beyond simple product prediction to provide detailed mechanistic,
    kinetic, and thermodynamic insights.
    """
    
    def __init__(self, xc: str = 'b3lyp', basis: str = '6-31g*'):
        """Initialize the advanced reaction system."""
        self.xc = xc
        self.basis = basis
        
        # Initialize all subsystems
        self.product_predictor = AdvancedProductPredictor()
        self.pathway_explorer = MultiplePathwayExplorer()
        self.side_reaction_predictor = SideReactionPredictor()
        self.ts_search_engine = RealTransitionStateSearchEngine(xc, basis)
        
        print(f"🚀 Advanced Reaction System Initialized")
        print(f"   Level of theory: {xc}/{basis}")
        print(f"   All subsystems loaded successfully")
    
    def analyze_reaction_comprehensive(self, reactant_smiles: List[str],
                                     target_products: List[str] = None,
                                     reaction_conditions: Dict[str, str] = None,
                                     include_ts_search: bool = True) -> ComprehensiveReactionAnalysis:
        """
        Perform comprehensive reaction analysis with all advanced features.
        
        Args:
            reactant_smiles: List of reactant SMILES strings
            target_products: Optional target products to analyze
            reaction_conditions: Reaction conditions for analysis
            include_ts_search: Whether to perform TS search (computationally expensive)
            
        Returns:
            Complete reaction analysis
        """
        print(f"\n🔬 COMPREHENSIVE REACTION ANALYSIS")
        print("=" * 70)
        print(f"Reactants: {' + '.join(reactant_smiles)}")
        if target_products:
            print(f"Targets: {' + '.join(target_products)}")
        if reaction_conditions:
            print(f"Conditions: {reaction_conditions}")
        
        # Step 1: Advanced Product Prediction
        print(f"\n📊 Step 1: Advanced Product Prediction")
        product_results = self.product_predictor.predict_all_products_advanced(reactant_smiles)
        product_predictions = product_results['predictions']
        
        if not product_predictions:
            return self._create_failed_analysis(reactant_smiles, "No viable products predicted")
        
        # Step 2: Multiple Pathway Exploration
        print(f"\n🗺️  Step 2: Multiple Pathway Exploration")
        pathways = self.pathway_explorer.explore_all_pathways(
            reactant_smiles, target_products, max_steps=3, max_pathways=5
        )
        
        # Step 3: Side Reaction Analysis
        print(f"\n⚠️  Step 3: Side Reaction Analysis")
        # Analyze side reactions for the best predicted reaction
        best_prediction = product_predictions[0] if product_predictions else None
        if best_prediction:
            # Convert dict back to ReactionPrediction if needed
            if isinstance(best_prediction, dict):
                best_prediction = self._dict_to_reaction_prediction(best_prediction)
            
            side_analysis = self.side_reaction_predictor.predict_side_reactions(
                best_prediction, reaction_conditions
            )
        else:
            side_analysis = None
        
        # Step 4: Transition State Search (optional)
        transition_states = {}
        if include_ts_search and pathways:
            print(f"\n🔍 Step 4: Transition State Search")
            transition_states = self._perform_ts_searches(reactant_smiles, pathways)
        
        # Step 5: Overall Assessment
        print(f"\n📈 Step 5: Overall Assessment")
        overall_assessment = self._generate_overall_assessment(
            product_predictions, pathways, side_analysis, transition_states
        )
        
        # Step 6: Generate Recommendations
        recommendations = self._generate_recommendations(
            product_predictions, pathways, side_analysis, transition_states
        )
        
        # Create comprehensive analysis
        analysis = ComprehensiveReactionAnalysis(
            reactants=reactant_smiles,
            product_predictions=product_predictions,
            pathways=pathways,
            side_reaction_analysis=side_analysis,
            transition_states=transition_states,
            overall_assessment=overall_assessment,
            recommendations=recommendations
        )
        
        # Display final results
        self._display_comprehensive_results(analysis)
        
        return analysis
    
    def _dict_to_reaction_prediction(self, pred_dict: Dict) -> ReactionPrediction:
        """Convert dictionary back to ReactionPrediction object."""
        return ReactionPrediction(
            reaction_type=pred_dict.get('reaction_type', 'unknown'),
            description=pred_dict.get('description', ''),
            reactants=pred_dict.get('reactants', []),
            products=pred_dict.get('products', []),
            conditions=pred_dict.get('conditions', {}),
            confidence=pred_dict.get('confidence', 0.5),
            method=pred_dict.get('method', 'unknown'),
            mechanism_steps=pred_dict.get('mechanism_steps'),
            side_products=pred_dict.get('side_products'),
            activation_energy_estimate=pred_dict.get('activation_energy_estimate'),
            thermodynamic_favorability=pred_dict.get('thermodynamic_favorability'),
            feasibility_score=pred_dict.get('feasibility_score'),
            uncertainty=pred_dict.get('uncertainty')
        )
    
    def _create_failed_analysis(self, reactants: List[str], reason: str) -> ComprehensiveReactionAnalysis:
        """Create analysis object for failed cases."""
        return ComprehensiveReactionAnalysis(
            reactants=reactants,
            product_predictions=[],
            pathways=[],
            side_reaction_analysis=None,
            transition_states={},
            overall_assessment={'success': False, 'reason': reason},
            recommendations=[f"Analysis failed: {reason}"]
        )
    
    def _perform_ts_searches(self, reactant_smiles: List[str], 
                           pathways: List[ReactionPathway]) -> Dict[str, TransitionStateSearchResult]:
        """Perform transition state searches for viable pathways."""
        transition_states = {}
        
        # Convert SMILES to ASE Atoms objects
        try:
            reactant_atoms_list = []
            for smiles in reactant_smiles:
                atoms = smiles_to_ase(smiles)
                if atoms:
                    # Optimize geometry
                    optimized_atoms = optimize_geometry(atoms, self.xc, self.basis)
                    reactant_atoms_list.append(optimized_atoms)
            
            if len(reactant_atoms_list) < 2:
                print("   ⚠️  Insufficient valid reactant structures for TS search")
                return transition_states
            
            # For simplicity, combine first two reactants
            reactants_combined = reactant_atoms_list[0]  # Simplified
            
            # Search TS for top pathways
            for i, pathway in enumerate(pathways[:2]):  # Limit to top 2 for computational efficiency
                if not pathway.steps:
                    continue
                
                print(f"   Searching TS for {pathway.pathway_id}...")
                
                # Create product structure (simplified)
                first_step = pathway.steps[0]
                if first_step.products:
                    try:
                        product_atoms = smiles_to_ase(first_step.products[0])
                        if product_atoms:
                            product_atoms = optimize_geometry(product_atoms, self.xc, self.basis)
                            
                            # Perform TS search
                            ts_result = self.ts_search_engine.find_transition_state(
                                reactants_combined, product_atoms, method='neb_then_optimize'
                            )
                            
                            transition_states[pathway.pathway_id] = ts_result
                    except Exception as e:
                        print(f"   ❌ TS search failed for {pathway.pathway_id}: {e}")
                        continue
        
        except Exception as e:
            print(f"   ❌ TS search setup failed: {e}")
        
        return transition_states
    
    def _generate_overall_assessment(self, product_predictions: List[Dict],
                                   pathways: List[ReactionPathway],
                                   side_analysis: Optional[SideReactionAnalysis],
                                   transition_states: Dict) -> Dict:
        """Generate overall assessment of the reaction system."""
        assessment = {
            'success': True,
            'confidence_level': 'high',
            'feasibility': 'good',
            'selectivity': 'moderate',
            'complexity': 'moderate',
            'computational_cost': 'moderate'
        }
        
        # Assess based on product predictions
        if product_predictions:
            avg_confidence = np.mean([p.get('confidence', 0.5) for p in product_predictions])
            if avg_confidence > 0.8:
                assessment['confidence_level'] = 'high'
            elif avg_confidence > 0.6:
                assessment['confidence_level'] = 'moderate'
            else:
                assessment['confidence_level'] = 'low'
        
        # Assess based on pathways
        if pathways:
            avg_yield = np.mean([p.overall_yield_estimate for p in pathways])
            if avg_yield > 0.8:
                assessment['feasibility'] = 'excellent'
            elif avg_yield > 0.6:
                assessment['feasibility'] = 'good'
            else:
                assessment['feasibility'] = 'challenging'
        
        # Assess based on side reactions
        if side_analysis:
            if side_analysis.overall_selectivity_estimate > 0.9:
                assessment['selectivity'] = 'excellent'
            elif side_analysis.overall_selectivity_estimate > 0.7:
                assessment['selectivity'] = 'good'
            else:
                assessment['selectivity'] = 'poor'
        
        # Assess complexity
        total_steps = sum(len(p.steps) for p in pathways)
        if total_steps > 10:
            assessment['complexity'] = 'high'
        elif total_steps > 5:
            assessment['complexity'] = 'moderate'
        else:
            assessment['complexity'] = 'low'
        
        return assessment
    
    def _generate_recommendations(self, product_predictions: List[Dict],
                                pathways: List[ReactionPathway],
                                side_analysis: Optional[SideReactionAnalysis],
                                transition_states: Dict) -> List[str]:
        """Generate actionable recommendations."""
        recommendations = []
        
        # Product-based recommendations
        if product_predictions:
            best_pred = product_predictions[0]
            recommendations.append(f"Primary recommendation: Pursue {best_pred.get('reaction_type', 'unknown')} pathway")
            
            if best_pred.get('confidence', 0) < 0.7:
                recommendations.append("Consider experimental validation due to moderate confidence")
        
        # Pathway-based recommendations
        if pathways:
            best_pathway = pathways[0]
            if best_pathway.overall_yield_estimate < 0.6:
                recommendations.append("Optimize reaction conditions to improve yield")
            
            if best_pathway.total_time_estimate > 12:
                recommendations.append("Consider catalyst screening to reduce reaction time")
        
        # Side reaction recommendations
        if side_analysis and side_analysis.overall_selectivity_estimate < 0.8:
            recommendations.append("Address selectivity issues - see side reaction analysis")
            if side_analysis.recommended_conditions:
                recommendations.append("Implement recommended reaction conditions")
        
        # TS-based recommendations
        successful_ts = [k for k, v in transition_states.items() if v.success]
        if successful_ts:
            recommendations.append(f"TS calculations confirm feasibility for {len(successful_ts)} pathway(s)")
        elif transition_states:
            recommendations.append("Consider alternative reaction conditions - high activation barriers predicted")
        
        return recommendations

    def _display_comprehensive_results(self, analysis: ComprehensiveReactionAnalysis):
        """Display comprehensive analysis results."""
        print(f"\n🎯 COMPREHENSIVE ANALYSIS RESULTS")
        print("=" * 70)

        # Overall Assessment
        assessment = analysis.overall_assessment
        print(f"Overall Success: {'✅' if assessment.get('success', False) else '❌'}")
        print(f"Confidence Level: {assessment.get('confidence_level', 'unknown')}")
        print(f"Feasibility: {assessment.get('feasibility', 'unknown')}")
        print(f"Selectivity: {assessment.get('selectivity', 'unknown')}")
        print(f"Complexity: {assessment.get('complexity', 'unknown')}")

        # Summary Statistics
        print(f"\n📊 SUMMARY STATISTICS")
        print(f"Product predictions: {len(analysis.product_predictions)}")
        print(f"Viable pathways: {len(analysis.pathways)}")
        print(f"TS calculations: {len(analysis.transition_states)}")

        if analysis.side_reaction_analysis:
            print(f"Side reactions identified: {len(analysis.side_reaction_analysis.side_reactions)}")
            print(f"Overall selectivity: {analysis.side_reaction_analysis.overall_selectivity_estimate:.1%}")

        # Top Recommendations
        print(f"\n💡 KEY RECOMMENDATIONS")
        for i, rec in enumerate(analysis.recommendations[:5], 1):
            print(f"{i}. {rec}")

        print("=" * 70)

    def export_comprehensive_analysis(self, analysis: ComprehensiveReactionAnalysis,
                                    filename: str):
        """Export comprehensive analysis to JSON file."""
        # Convert to serializable format
        export_data = {
            'reactants': analysis.reactants,
            'product_predictions': analysis.product_predictions,
            'pathways': [asdict(p) for p in analysis.pathways],
            'side_reaction_analysis': asdict(analysis.side_reaction_analysis) if analysis.side_reaction_analysis else None,
            'transition_states': {k: asdict(v) for k, v in analysis.transition_states.items()},
            'overall_assessment': analysis.overall_assessment,
            'recommendations': analysis.recommendations
        }

        # Handle non-serializable objects
        def clean_for_json(obj):
            if isinstance(obj, dict):
                return {k: clean_for_json(v) for k, v in obj.items()}
            elif isinstance(obj, list):
                return [clean_for_json(item) for item in obj]
            elif hasattr(obj, '__dict__'):
                return clean_for_json(obj.__dict__)
            elif isinstance(obj, (np.ndarray, Atoms)):
                return str(obj)  # Convert to string representation
            else:
                return obj

        cleaned_data = clean_for_json(export_data)

        with open(filename, 'w') as f:
            json.dump(cleaned_data, f, indent=2, default=str)

        print(f"✅ Comprehensive analysis exported to {filename}")

    def quick_reaction_check(self, reactant_smiles: List[str]) -> Dict:
        """Quick reaction feasibility check without full analysis."""
        print(f"\n⚡ QUICK REACTION CHECK")
        print(f"Reactants: {' + '.join(reactant_smiles)}")

        # Quick product prediction
        product_results = self.product_predictor.predict_all_products_advanced(reactant_smiles)

        if not product_results['predictions']:
            return {
                'feasible': False,
                'reason': 'No viable products predicted',
                'confidence': 0.0,
                'recommendation': 'Try different reactants or conditions'
            }

        best_prediction = product_results['predictions'][0]

        quick_check = {
            'feasible': True,
            'best_product': best_prediction.get('products', ['unknown'])[0],
            'reaction_type': best_prediction.get('reaction_type', 'unknown'),
            'confidence': best_prediction.get('confidence', 0.0),
            'estimated_yield': best_prediction.get('confidence', 0.0) * 0.8,  # Rough estimate
            'recommendation': 'Proceed with full analysis' if best_prediction.get('confidence', 0) > 0.7 else 'Consider optimization'
        }

        print(f"✅ Feasible: {quick_check['feasible']}")
        print(f"Best product: {quick_check['best_product']}")
        print(f"Confidence: {quick_check['confidence']:.2f}")
        print(f"Recommendation: {quick_check['recommendation']}")

        return quick_check

    def benchmark_system_performance(self, test_reactions: List[Tuple[List[str], List[str]]]) -> Dict:
        """Benchmark the system performance on test reactions."""
        print(f"\n🏃 BENCHMARKING SYSTEM PERFORMANCE")
        print(f"Testing {len(test_reactions)} reactions...")

        results = {
            'total_reactions': len(test_reactions),
            'successful_predictions': 0,
            'average_confidence': 0.0,
            'average_pathways': 0.0,
            'computation_times': [],
            'detailed_results': []
        }

        import time

        for i, (reactants, expected_products) in enumerate(test_reactions):
            print(f"\nTest {i+1}/{len(test_reactions)}: {' + '.join(reactants)}")

            start_time = time.time()

            try:
                # Quick analysis for benchmarking
                quick_result = self.quick_reaction_check(reactants)

                computation_time = time.time() - start_time
                results['computation_times'].append(computation_time)

                if quick_result['feasible']:
                    results['successful_predictions'] += 1

                results['detailed_results'].append({
                    'reactants': reactants,
                    'expected_products': expected_products,
                    'predicted_feasible': quick_result['feasible'],
                    'confidence': quick_result.get('confidence', 0.0),
                    'computation_time': computation_time
                })

                print(f"   ✅ Completed in {computation_time:.2f}s")

            except Exception as e:
                print(f"   ❌ Failed: {e}")
                results['detailed_results'].append({
                    'reactants': reactants,
                    'expected_products': expected_products,
                    'error': str(e)
                })

        # Calculate statistics
        if results['detailed_results']:
            confidences = [r.get('confidence', 0) for r in results['detailed_results'] if 'confidence' in r]
            results['average_confidence'] = np.mean(confidences) if confidences else 0.0

        results['success_rate'] = results['successful_predictions'] / results['total_reactions']
        results['average_computation_time'] = np.mean(results['computation_times']) if results['computation_times'] else 0.0

        print(f"\n📊 BENCHMARK RESULTS")
        print(f"Success rate: {results['success_rate']:.1%}")
        print(f"Average confidence: {results['average_confidence']:.2f}")
        print(f"Average computation time: {results['average_computation_time']:.2f}s")

        return results
