#!/usr/bin/env python3
"""
Comprehensive Test Suite for Advanced DFT Reaction System
Tests all new professional features:
- Advanced Product Prediction
- Multiple Pathway Explorer
- Side Reaction Predictor
- Real Transition State Search
"""

import sys
import traceback
from typing import List, Dict

def test_advanced_product_prediction():
    """Test the advanced product prediction system."""
    print("\n🧪 TESTING: Advanced Product Prediction")
    print("-" * 50)
    
    try:
        from real_product_predictor import AdvancedProductPredictor
        
        predictor = AdvancedProductPredictor()
        
        # Test case 1: Simple esterification
        reactants = ['CCO', 'CC(=O)O']  # Ethanol + Acetic acid
        print(f"Test 1: {' + '.join(reactants)}")
        
        results = predictor.predict_all_products_advanced(reactants)
        
        if results['status'] == 'success':
            print(f"✅ Success: {results['total_pathways']} pathways found")
            print(f"   High confidence: {results['high_confidence_pathways']}")
            if results['best_prediction']:
                best = results['best_prediction']
                print(f"   Best: {best['reaction_type']} (conf: {best['confidence']:.2f})")
        else:
            print(f"❌ Failed: {results.get('analysis', 'Unknown error')}")
        
        # Test case 2: More complex reaction
        reactants2 = ['CC(=O)O', 'CCN']  # Acetic acid + Ethylamine
        print(f"\nTest 2: {' + '.join(reactants2)}")
        
        results2 = predictor.predict_all_products_advanced(reactants2)
        
        if results2['status'] == 'success':
            print(f"✅ Success: {results2['total_pathways']} pathways found")
        else:
            print(f"❌ Failed: {results2.get('analysis', 'Unknown error')}")
        
        return True
        
    except Exception as e:
        print(f"❌ Advanced Product Prediction test failed: {e}")
        traceback.print_exc()
        return False

def test_multiple_pathway_explorer():
    """Test the multiple pathway explorer."""
    print("\n🗺️  TESTING: Multiple Pathway Explorer")
    print("-" * 50)
    
    try:
        from multiple_pathway_explorer import MultiplePathwayExplorer
        
        explorer = MultiplePathwayExplorer()
        
        # Test pathway exploration
        starting_materials = ['CCO', 'CC(=O)O']
        target_products = ['CC(=O)OCC']  # Ethyl acetate
        
        print(f"Exploring pathways: {' + '.join(starting_materials)} → {' + '.join(target_products)}")
        
        pathways = explorer.explore_all_pathways(
            starting_materials=starting_materials,
            target_products=target_products,
            max_steps=2,
            max_pathways=3
        )
        
        if pathways:
            print(f"✅ Success: {len(pathways)} pathways found")
            for i, pathway in enumerate(pathways[:2]):
                print(f"   Pathway {i+1}: {len(pathway.steps)} steps, "
                      f"yield: {pathway.overall_yield_estimate:.1%}")
        else:
            print("❌ No pathways found")
        
        return len(pathways) > 0
        
    except Exception as e:
        print(f"❌ Multiple Pathway Explorer test failed: {e}")
        traceback.print_exc()
        return False

def test_side_reaction_predictor():
    """Test the side reaction predictor."""
    print("\n⚠️  TESTING: Side Reaction Predictor")
    print("-" * 50)
    
    try:
        from side_reaction_predictor import SideReactionPredictor
        from real_product_predictor import ReactionPrediction
        
        predictor = SideReactionPredictor()
        
        # Create a test reaction
        test_reaction = ReactionPrediction(
            reaction_type='esterification',
            description='Ethanol + Acetic acid → Ethyl acetate',
            reactants=['CCO', 'CC(=O)O'],
            products=['CC(=O)OCC'],
            conditions={'temperature': 'moderate', 'catalyst': 'acid'},
            confidence=0.85,
            method='template_based'
        )
        
        print(f"Analyzing side reactions for: {test_reaction.description}")
        
        # Test with different conditions
        conditions = {'temperature': 'high', 'catalyst': 'strong_acid'}
        
        analysis = predictor.predict_side_reactions(test_reaction, conditions)
        
        if analysis:
            print(f"✅ Success: {len(analysis.side_reactions)} side reactions identified")
            print(f"   Overall selectivity: {analysis.overall_selectivity_estimate:.1%}")
            print(f"   Major concerns: {len(analysis.major_concerns)}")
        else:
            print("❌ Side reaction analysis failed")
        
        return analysis is not None
        
    except Exception as e:
        print(f"❌ Side Reaction Predictor test failed: {e}")
        traceback.print_exc()
        return False

def test_transition_state_search():
    """Test the transition state search engine."""
    print("\n🔍 TESTING: Transition State Search")
    print("-" * 50)
    
    try:
        from transition_state_search import RealTransitionStateSearchEngine
        from input_handler import smiles_to_ase
        from molecule_optimizer import optimize_geometry
        
        ts_engine = RealTransitionStateSearchEngine(xc='b3lyp', basis='sto-3g')
        
        # Create simple test molecules
        print("Creating test molecules...")
        
        # Simple test case - this is computationally expensive, so we'll use a simplified test
        reactant_smiles = 'CCO'
        product_smiles = 'CC=O'  # Simplified transformation
        
        reactant_atoms = smiles_to_ase(reactant_smiles)
        product_atoms = smiles_to_ase(product_smiles)
        
        if reactant_atoms and product_atoms:
            print("✅ Molecules created successfully")
            
            # Test different TS search methods
            methods = ['dimer', 'eigenvector_following']  # Skip NEB for speed
            
            for method in methods:
                print(f"Testing method: {method}")
                
                try:
                    result = ts_engine.find_transition_state(
                        reactant_atoms, product_atoms, method=method
                    )
                    
                    if result.success:
                        print(f"✅ {method}: Success")
                        if result.transition_state:
                            print(f"   Activation energy: {result.transition_state.activation_energy:.2f} kcal/mol")
                    else:
                        print(f"⚠️  {method}: Failed - {result.error_message}")
                        
                except Exception as e:
                    print(f"❌ {method}: Error - {e}")
            
            return True
        else:
            print("❌ Could not create test molecules")
            return False
        
    except Exception as e:
        print(f"❌ Transition State Search test failed: {e}")
        traceback.print_exc()
        return False

def test_integrated_system():
    """Test the integrated advanced reaction system."""
    print("\n🚀 TESTING: Integrated Advanced System")
    print("-" * 50)
    
    try:
        from advanced_reaction_system import AdvancedReactionSystem
        
        system = AdvancedReactionSystem(xc='b3lyp', basis='sto-3g')
        
        # Test quick check
        print("Testing quick reaction check...")
        reactants = ['CCO', 'CC(=O)O']
        
        quick_result = system.quick_reaction_check(reactants)
        
        if quick_result['feasible']:
            print(f"✅ Quick check: Feasible (conf: {quick_result['confidence']:.2f})")
        else:
            print(f"❌ Quick check: Not feasible - {quick_result['reason']}")
        
        # Test comprehensive analysis (without TS search for speed)
        print("\nTesting comprehensive analysis...")
        
        comprehensive_result = system.analyze_reaction_comprehensive(
            reactant_smiles=reactants,
            include_ts_search=False  # Skip for testing speed
        )
        
        if comprehensive_result.overall_assessment.get('success', False):
            print("✅ Comprehensive analysis: Success")
            print(f"   Product predictions: {len(comprehensive_result.product_predictions)}")
            print(f"   Pathways: {len(comprehensive_result.pathways)}")
            print(f"   Recommendations: {len(comprehensive_result.recommendations)}")
        else:
            print("❌ Comprehensive analysis failed")
        
        return True
        
    except Exception as e:
        print(f"❌ Integrated System test failed: {e}")
        traceback.print_exc()
        return False

def run_all_tests():
    """Run all tests and report results."""
    print("🧪 ADVANCED DFT REACTION SYSTEM - COMPREHENSIVE TEST SUITE")
    print("=" * 70)
    
    tests = [
        ("Advanced Product Prediction", test_advanced_product_prediction),
        ("Multiple Pathway Explorer", test_multiple_pathway_explorer),
        ("Side Reaction Predictor", test_side_reaction_predictor),
        ("Transition State Search", test_transition_state_search),
        ("Integrated System", test_integrated_system),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ {test_name} crashed: {e}")
            results[test_name] = False
    
    # Summary
    print(f"\n{'='*70}")
    print("🏁 TEST SUMMARY")
    print("=" * 70)
    
    passed = sum(results.values())
    total = len(results)
    
    for test_name, passed_test in results.items():
        status = "✅ PASS" if passed_test else "❌ FAIL"
        print(f"{status} {test_name}")
    
    print(f"\nOverall: {passed}/{total} tests passed ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("🎉 ALL TESTS PASSED! The advanced system is ready for professional use.")
    else:
        print("⚠️  Some tests failed. Please review the errors above.")
    
    return passed == total

if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
